package com.maplecan.scraper.controller;

import com.maplecan.scraper.service.OpenAIVisionService;
import com.maplecan.scraper.service.ScreenshotService;
import com.maplecan.scraper.service.SupabaseStorageService;
import com.maplecan.scraper.service.EmailService;
import com.maplecan.scraper.model.email.EmailRequest;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;

import java.time.LocalDateTime;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * Test controller for AI price drop detection functionality
 * This controller provides endpoints to manually test the OpenAI Vision integration
 */
@Slf4j
@RestController
@RequestMapping("/api/test/ai")
@RequiredArgsConstructor
public class TestAIController {

    private final OpenAIVisionService openAIVisionService;
    private final ScreenshotService screenshotService;
    private final SupabaseStorageService supabaseStorageService;
    private final EmailService emailService;

    /**
     * Test endpoint to analyze two image URLs for price drops using AI
     * 
     * @param request Test request containing image URLs and website URL
     * @return AI analysis results
     */
    @PostMapping("/analyze-price-drop")
    public ResponseEntity<Map<String, Object>> testPriceDropAnalysis(@RequestBody TestPriceDropRequest request) {
        log.info("Testing AI price drop analysis for images: {} vs {}", request.getBaselineImageUrl(), request.getNewImageUrl());
        
        try {
            // Validate input
            if (request.getBaselineImageUrl() == null || request.getBaselineImageUrl().trim().isEmpty()) {
                return ResponseEntity.badRequest().body(Map.of("error", "Baseline image URL is required"));
            }
            
            if (request.getNewImageUrl() == null || request.getNewImageUrl().trim().isEmpty()) {
                return ResponseEntity.badRequest().body(Map.of("error", "New image URL is required"));
            }
            
            String websiteUrl = request.getWebsiteUrl() != null ? request.getWebsiteUrl() : "https://example.com";
            
            // Perform AI analysis
            OpenAIVisionService.PriceDropAnalysis analysis = openAIVisionService.analyzePriceDrop(
                request.getBaselineImageUrl(),
                request.getNewImageUrl(),
                websiteUrl
            );

            // Create response
            Map<String, Object> response = new HashMap<>();

            if (analysis != null) {
                response.put("success", true);
                Map<String, Object> analysisMap = new HashMap<>();
                analysisMap.put("hasPriceDrop", analysis.isHasPriceDrop());
                analysisMap.put("confidence", analysis.getConfidence());
                analysisMap.put("oldPrice", analysis.getOldPrice());
                analysisMap.put("newPrice", analysis.getNewPrice());
                analysisMap.put("reasoning", analysis.getReasoning());
                analysisMap.put("rawResponse", analysis.getRawResponse());
                response.put("analysis", analysisMap);
            } else {
                response.put("success", false);
                response.put("error", "AI analysis returned null - check API key configuration");
            }
            response.put("testParameters", Map.of(
                "baselineImageUrl", request.getBaselineImageUrl(),
                "newImageUrl", request.getNewImageUrl(),
                "websiteUrl", websiteUrl
            ));
            
            log.info("AI analysis completed - Price drop: {}, Confidence: {}", 
                    analysis.isHasPriceDrop(), analysis.getConfidence());
            
            return ResponseEntity.ok(response);
            
        } catch (Exception e) {
            log.error("Error during AI price drop analysis test: {}", e.getMessage(), e);
            
            Map<String, Object> errorResponse = new HashMap<>();
            errorResponse.put("success", false);
            errorResponse.put("error", e.getMessage());
            errorResponse.put("testParameters", Map.of(
                "baselineImageUrl", request.getBaselineImageUrl(),
                "newImageUrl", request.getNewImageUrl(),
                "websiteUrl", request.getWebsiteUrl() != null ? request.getWebsiteUrl() : "https://example.com"
            ));
            
            return ResponseEntity.status(500).body(errorResponse);
        }
    }

    /**
     * Health check endpoint to verify OpenAI service configuration
     */
    @GetMapping("/health")
    public ResponseEntity<Map<String, Object>> healthCheck() {
        Map<String, Object> response = new HashMap<>();
        
        try {
            // Test with dummy URLs to check if service is configured
            String testResult = "OpenAI service is available";
            
            response.put("success", true);
            response.put("message", testResult);
            response.put("timestamp", java.time.LocalDateTime.now().toString());
            
            return ResponseEntity.ok(response);
            
        } catch (Exception e) {
            log.error("Health check failed: {}", e.getMessage(), e);
            
            response.put("success", false);
            response.put("error", e.getMessage());
            response.put("timestamp", java.time.LocalDateTime.now().toString());
            
            return ResponseEntity.status(500).body(response);
        }
    }

    /**
     * Test endpoint to capture a screenshot using current configuration
     */
    @PostMapping("/test-screenshot")
    public ResponseEntity<Map<String, Object>> testScreenshot(@RequestBody Map<String, Object> request) {
        String url = (String) request.get("url");
        if (url == null || url.trim().isEmpty()) {
            Map<String, Object> errorResponse = new HashMap<>();
            errorResponse.put("success", false);
            errorResponse.put("message", "URL is required");
            return ResponseEntity.badRequest().body(errorResponse);
        }

        // Get fullPage parameter, default to false (viewport screenshot)
        Boolean fullPageObj = (Boolean) request.get("fullPage");
        boolean fullPage = fullPageObj != null ? fullPageObj : false;

        try {
            log.info("Testing screenshot capture for URL: {} (fullPage: {})", url, fullPage);

            // Capture screenshot using the configured service with screenshot type
            byte[] screenshotBytes = screenshotService.captureScreenshotBytes(url, fullPage);

            // Upload to Supabase for testing
            String screenshotUrl = supabaseStorageService.uploadFile(
                screenshotBytes,
                "test-screenshots/" + System.currentTimeMillis() + ".jpg",
                "image/jpeg"
            );

            Map<String, Object> response = new HashMap<>();
            response.put("success", true);
            response.put("screenshotUrl", screenshotUrl);
            response.put("originalUrl", url);
            response.put("screenshotSize", screenshotBytes.length);
            response.put("fullPage", fullPage);
            response.put("timestamp", LocalDateTime.now());

            log.info("Successfully captured test screenshot: {} bytes (fullPage: {})", screenshotBytes.length, fullPage);
            return ResponseEntity.ok(response);

        } catch (Exception e) {
            log.error("Failed to capture test screenshot for URL: {} (fullPage: {})", url, fullPage, e);
            Map<String, Object> errorResponse = new HashMap<>();
            errorResponse.put("success", false);
            errorResponse.put("message", "Failed to capture screenshot: " + e.getMessage());
            return ResponseEntity.status(500).body(errorResponse);
        }
    }

    /**
     * Debug endpoint to see what the AI actually sees in images
     */
    @PostMapping("/debug-vision")
    public ResponseEntity<Map<String, Object>> debugVision(@RequestBody TestPriceDropRequest request) {
        log.info("Debug vision analysis for images: {} vs {}", request.getBaselineImageUrl(), request.getNewImageUrl());

        Map<String, Object> response = new HashMap<>();
        response.put("success", false);

        try {
            // Validate input
            if (request.getBaselineImageUrl() == null || request.getBaselineImageUrl().trim().isEmpty()) {
                response.put("error", "Baseline image URL is required");
                return ResponseEntity.badRequest().body(response);
            }

            if (request.getNewImageUrl() == null || request.getNewImageUrl().trim().isEmpty()) {
                response.put("error", "New image URL is required");
                return ResponseEntity.badRequest().body(response);
            }

            // Call debug vision analysis
            String debugResult = openAIVisionService.debugVisionAnalysis(
                request.getBaselineImageUrl(),
                request.getNewImageUrl()
            );

            response.put("success", true);
            response.put("debugAnalysis", debugResult);
            response.put("testParameters", Map.of(
                "baselineImageUrl", request.getBaselineImageUrl(),
                "newImageUrl", request.getNewImageUrl(),
                "websiteUrl", request.getWebsiteUrl() != null ? request.getWebsiteUrl() : "Not provided"
            ));

            log.info("Debug vision analysis completed successfully");
            return ResponseEntity.ok(response);

        } catch (Exception e) {
            log.error("Error during debug vision analysis", e);
            response.put("error", "Debug analysis failed: " + e.getMessage());
            return ResponseEntity.status(500).body(response);
        }
    }

    /**
     * Get sample test data for manual testing
     */
    @GetMapping("/sample-data")
    public ResponseEntity<Map<String, Object>> getSampleTestData() {
        Map<String, Object> response = new HashMap<>();

        // Provide sample image URLs for testing
        response.put("sampleTests", Map.of(
            "test1", Map.of(
                "description", "Test with sample e-commerce images",
                "baselineImageUrl", "https://example.com/baseline-image.jpg",
                "newImageUrl", "https://example.com/new-image.jpg",
                "websiteUrl", "https://example-store.com/product/123",
                "expectedResult", "Should detect price changes if any exist"
            ),
            "test2", Map.of(
                "description", "Test with identical images",
                "baselineImageUrl", "https://example.com/same-image.jpg",
                "newImageUrl", "https://example.com/same-image.jpg",
                "websiteUrl", "https://example-store.com/product/456",
                "expectedResult", "Should not detect price drop"
            )
        ));

        response.put("instructions", Map.of(
            "step1", "Use POST /api/test/ai/analyze-price-drop with image URLs",
            "step2", "Check the response for AI analysis results",
            "step3", "Verify confidence levels and reasoning",
            "note", "Make sure OPENAI_API_KEY environment variable is set"
        ));

        response.put("apiInfo", Map.of(
            "implementation", "Direct OpenAI API calls (no third-party libraries)",
            "model", "gpt-4o (optimized for vision tasks)",
            "endpoint", "https://api.openai.com/v1/chat/completions",
            "features", "JSON response format, temperature 0.1, optimized prompts",
            "documentation", "https://platform.openai.com/docs/guides/images-vision"
        ));

        return ResponseEntity.ok(response);
    }

    /**
     * Test image accessibility before sending to OpenAI
     */
    @PostMapping("/test-images")
    public ResponseEntity<Map<String, Object>> testImageAccessibility(@RequestBody TestPriceDropRequest request) {
        Map<String, Object> response = new HashMap<>();

        try {
            boolean baselineAccessible = openAIVisionService.testImageAccessibility(request.getBaselineImageUrl());
            boolean newImageAccessible = openAIVisionService.testImageAccessibility(request.getNewImageUrl());

            response.put("success", true);
            response.put("baselineImage", Map.of(
                "url", request.getBaselineImageUrl(),
                "accessible", baselineAccessible
            ));
            response.put("newImage", Map.of(
                "url", request.getNewImageUrl(),
                "accessible", newImageAccessible
            ));
            response.put("bothAccessible", baselineAccessible && newImageAccessible);

            if (!baselineAccessible || !newImageAccessible) {
                response.put("warning", "One or both images are not accessible. OpenAI may not be able to analyze them.");
                response.put("suggestions", List.of(
                    "Check if images require authentication",
                    "Verify CORS settings on your storage",
                    "Consider making images publicly accessible",
                    "Check image file formats (JPG, PNG, GIF, WebP supported)"
                ));
            }

            return ResponseEntity.ok(response);

        } catch (Exception e) {
            log.error("Error testing image accessibility: {}", e.getMessage(), e);
            response.put("success", false);
            response.put("error", e.getMessage());
            return ResponseEntity.status(500).body(response);
        }
    }

    /**
     * Debug endpoint to show the exact API request that would be sent to OpenAI
     */
    @PostMapping("/debug-request")
    public ResponseEntity<Map<String, Object>> debugApiRequest(@RequestBody TestPriceDropRequest request) {
        Map<String, Object> response = new HashMap<>();

        try {
            // Show what the actual API request would look like (optimized version)
            Map<String, Object> apiRequest = new HashMap<>();
            apiRequest.put("model", "gpt-4o");
            apiRequest.put("messages", List.of(
                Map.of(
                    "role", "system",
                    "content", "You are an e-commerce price-monitoring assistant. Extract visible CAD prices from screenshots, compare them, and determine if the price changed."
                ),
                Map.of(
                    "role", "user",
                    "content", List.of(
                        Map.of(
                            "type", "image_url",
                            "image_url", Map.of("url", request.getBaselineImageUrl())
                        ),
                        Map.of(
                            "type", "image_url",
                            "image_url", Map.of("url", request.getNewImageUrl())
                        ),
                        Map.of(
                            "type", "text",
                            "text", "Compare these two screenshots. Return JSON only."
                        )
                    )
                )
            ));
            apiRequest.put("max_tokens", 150);
            apiRequest.put("temperature", 0.1);
            apiRequest.put("response_format", Map.of("type", "json_object"));

            response.put("success", true);
            response.put("apiEndpoint", "https://api.openai.com/v1/chat/completions");
            response.put("method", "POST");
            response.put("headers", Map.of(
                "Content-Type", "application/json",
                "Authorization", "Bearer YOUR_OPENAI_API_KEY"
            ));
            response.put("requestBody", apiRequest);
            response.put("note", "This shows the exact request format sent to OpenAI API");

            return ResponseEntity.ok(response);

        } catch (Exception e) {
            response.put("success", false);
            response.put("error", e.getMessage());
            return ResponseEntity.status(500).body(response);
        }
    }

    /**
     * Test endpoint to send a price drop notification email
     */
    @PostMapping("/test-email")
    public ResponseEntity<Map<String, Object>> testPriceDropEmail(@RequestBody TestEmailRequest request) {
        log.info("Testing price drop email notification");

        try {
            // Create mock AI analysis with the provided data
            OpenAIVisionService.PriceDropAnalysis mockAnalysis = OpenAIVisionService.PriceDropAnalysis.builder()
                .hasPriceDrop(true)
                .confidence(0.9)
                .oldPrice(request.getOldPrice() != null ? request.getOldPrice() : "450.00")
                .newPrice(request.getNewPrice() != null ? request.getNewPrice() : "350.00")
                .summary(request.getSummary() != null ? request.getSummary() : "Significant price drop detected - product now on sale")
                .reasoning("Test email notification")
                .rawResponse("Test response")
                .build();

            // Create HTML email content
            String htmlContent = createTestEmailHtml(request, mockAnalysis);
            String textContent = createTestEmailText(request, mockAnalysis);

            // Create email request with professional styling
            String websiteName = extractDomainFromUrl(request.getWebsiteUrl());
            String subject = mockAnalysis.isHasPriceDrop() ?
                "Price Alert: " + websiteName + " - Price Drop Detected" :
                "Website Alert: " + websiteName + " - Change Detected";

            EmailRequest emailRequest = EmailRequest.builder()
                .fromEmail("<EMAIL>")
                .fromName("BargainHawk Price Monitoring")
                .toEmail("<EMAIL>")
                .toName("Customer")
                .subject(subject)
                .htmlContent(htmlContent)
                .textContent(textContent)
                .build();

            // Send email
            emailService.sendEmail(emailRequest);

            Map<String, Object> response = new HashMap<>();
            response.put("success", true);
            response.put("message", "Test email sent <NAME_EMAIL>");
            response.put("aiAnalysis", Map.of(
                "hasPriceDrop", mockAnalysis.isHasPriceDrop(),
                "confidence", mockAnalysis.getConfidence(),
                "oldPrice", mockAnalysis.getOldPrice(),
                "newPrice", mockAnalysis.getNewPrice(),
                "summary", mockAnalysis.getSummary()
            ));

            log.info("Test price drop email sent successfully");
            return ResponseEntity.ok(response);

        } catch (Exception e) {
            log.error("Error sending test email: {}", e.getMessage(), e);
            Map<String, Object> errorResponse = new HashMap<>();
            errorResponse.put("success", false);
            errorResponse.put("error", "Failed to send test email: " + e.getMessage());
            return ResponseEntity.status(500).body(errorResponse);
        }
    }

    private String createTestEmailHtml(TestEmailRequest request, OpenAIVisionService.PriceDropAnalysis aiAnalysis) {
        return String.format("""
            <!DOCTYPE html>
            <html>
            <head>
                <meta charset="UTF-8">
                <meta name="viewport" content="width=device-width, initial-scale=1.0">
                <title>Price Drop Alert</title>
            </head>
            <body style="font-family: Arial, sans-serif; line-height: 1.6; color: #333; max-width: 600px; margin: 0 auto; padding: 20px;">
                <div style="background: linear-gradient(135deg, #667eea 0%%, #764ba2 100%%); color: white; padding: 30px; text-align: center; border-radius: 10px 10px 0 0;">
                    <h1 style="margin: 0; font-size: 28px;">🎉 Price Drop Detected!</h1>
                    <p style="margin: 10px 0 0 0; font-size: 16px; opacity: 0.9;">AI-powered website monitoring alert</p>
                </div>

                <div style="background: #f8f9fa; padding: 30px; border-radius: 0 0 10px 10px;">
                    <h2 style="color: #2c3e50; margin-top: 0;">Website Change Alert</h2>

                    <div style="background: white; padding: 20px; border-radius: 8px; margin: 20px 0; border-left: 4px solid #27ae60;">
                        <h3 style="color: #27ae60; margin-top: 0;">✅ AI Analysis Results</h3>
                        <p><strong>Price Drop Detected:</strong> %s</p>
                        <p><strong>Confidence Level:</strong> %.1f%%</p>
                        <p><strong>AI Summary:</strong> %s</p>
                        <p><strong>Old Price:</strong> $%s</p>
                        <p><strong>New Price:</strong> $%s</p>
                    </div>

                    <div style="background: white; padding: 20px; border-radius: 8px; margin: 20px 0;">
                        <h3 style="color: #2c3e50; margin-top: 0;">📋 Website Details</h3>
                        <p><strong>Website:</strong> <a href="%s" style="color: #3498db; text-decoration: none;">%s</a></p>
                        <p><strong>Test Mode:</strong> Email validation</p>
                    </div>

                    <div style="background: white; padding: 20px; border-radius: 8px; margin: 20px 0;">
                        <h3 style="color: #2c3e50; margin-top: 0;">📸 Screenshot Comparison</h3>
                        <div style="display: flex; gap: 20px; flex-wrap: wrap;">
                            <div style="flex: 1; min-width: 250px;">
                                <h4 style="color: #7f8c8d; margin-bottom: 10px;">Baseline Screenshot</h4>
                                <img src="%s" alt="Baseline Screenshot" style="width: 100%%; max-width: 300px; border: 2px solid #ecf0f1; border-radius: 5px;">
                            </div>
                            <div style="flex: 1; min-width: 250px;">
                                <h4 style="color: #7f8c8d; margin-bottom: 10px;">New Screenshot</h4>
                                <img src="%s" alt="New Screenshot" style="width: 100%%; max-width: 300px; border: 2px solid #ecf0f1; border-radius: 5px;">
                            </div>
                        </div>
                    </div>

                    <div style="text-align: center; margin: 30px 0;">
                        <a href="%s" style="background: #3498db; color: white; padding: 15px 30px; text-decoration: none; border-radius: 5px; font-weight: bold; display: inline-block;">View Website</a>
                        <a href="https://bargainhawk.ca/dashboard" style="background: #95a5a6; color: white; padding: 15px 30px; text-decoration: none; border-radius: 5px; font-weight: bold; display: inline-block; margin-left: 10px;">View Dashboard</a>
                    </div>

                    <div style="background: #ecf0f1; padding: 15px; border-radius: 5px; margin-top: 30px; font-size: 14px; color: #7f8c8d;">
                        <p style="margin: 0;"><strong>Note:</strong> This is a test email for validation purposes. The AI analyzes screenshots to detect price changes with high accuracy.</p>
                    </div>
                </div>

                <div style="text-align: center; margin-top: 30px; font-size: 12px; color: #95a5a6;">
                    <p>This email was sent by BargainHawk Website Monitoring</p>
                    <p><a href="https://bargainhawk.ca/unsubscribe" style="color: #95a5a6;">Unsubscribe</a> | <a href="https://bargainhawk.ca" style="color: #95a5a6;">Visit BargainHawk</a></p>
                </div>
            </body>
            </html>
            """,
            aiAnalysis.isHasPriceDrop() ? "Yes" : "No",
            aiAnalysis.getConfidence() * 100,
            aiAnalysis.getSummary(),
            aiAnalysis.getOldPrice(),
            aiAnalysis.getNewPrice(),
            request.getWebsiteUrl(),
            extractDomainFromUrl(request.getWebsiteUrl()),
            request.getBaselineImageUrl(),
            request.getNewImageUrl(),
            request.getWebsiteUrl()
        );
    }

    /**
     * Request DTO for price drop analysis testing
     */
    public static class TestPriceDropRequest {
        private String baselineImageUrl;
        private String newImageUrl;
        private String websiteUrl;

        // Constructors
        public TestPriceDropRequest() {}

        public TestPriceDropRequest(String baselineImageUrl, String newImageUrl, String websiteUrl) {
            this.baselineImageUrl = baselineImageUrl;
            this.newImageUrl = newImageUrl;
            this.websiteUrl = websiteUrl;
        }

        // Getters and Setters
        public String getBaselineImageUrl() {
            return baselineImageUrl;
        }

        public void setBaselineImageUrl(String baselineImageUrl) {
            this.baselineImageUrl = baselineImageUrl;
        }

        public String getNewImageUrl() {
            return newImageUrl;
        }

        public void setNewImageUrl(String newImageUrl) {
            this.newImageUrl = newImageUrl;
        }

        public String getWebsiteUrl() {
            return websiteUrl;
        }

        public void setWebsiteUrl(String websiteUrl) {
            this.websiteUrl = websiteUrl;
        }
    }

    private String createTestEmailText(TestEmailRequest request, OpenAIVisionService.PriceDropAnalysis aiAnalysis) {
        return String.format("""
            PRICE DROP ALERT!

            Our AI-powered website monitoring system has detected a price drop on one of your tracked websites.

            AI Analysis Results:
            - Price Drop Detected: %s
            - Confidence Level: %.1f%%
            - AI Summary: %s
            - Old Price: $%s
            - New Price: $%s

            Website Details:
            - Website: %s
            - Test Mode: Email validation

            Visit the website: %s
            View your dashboard: https://bargainhawk.ca/dashboard

            This is a test email for validation purposes.
            To unsubscribe: https://bargainhawk.ca/unsubscribe
            """,
            aiAnalysis.isHasPriceDrop() ? "Yes" : "No",
            aiAnalysis.getConfidence() * 100,
            aiAnalysis.getSummary(),
            aiAnalysis.getOldPrice(),
            aiAnalysis.getNewPrice(),
            request.getWebsiteUrl(),
            request.getWebsiteUrl()
        );
    }

    private String extractDomainFromUrl(String url) {
        try {
            java.net.URL urlObj = new java.net.URL(url);
            return urlObj.getHost();
        } catch (Exception e) {
            return url;
        }
    }

    /**
     * Request class for testing email notifications
     */
    public static class TestEmailRequest {
        private String websiteUrl;
        private String baselineImageUrl;
        private String newImageUrl;
        private String oldPrice;
        private String newPrice;
        private String summary;

        // Getters and setters
        public String getWebsiteUrl() { return websiteUrl; }
        public void setWebsiteUrl(String websiteUrl) { this.websiteUrl = websiteUrl; }

        public String getBaselineImageUrl() { return baselineImageUrl; }
        public void setBaselineImageUrl(String baselineImageUrl) { this.baselineImageUrl = baselineImageUrl; }

        public String getNewImageUrl() { return newImageUrl; }
        public void setNewImageUrl(String newImageUrl) { this.newImageUrl = newImageUrl; }

        public String getOldPrice() { return oldPrice; }
        public void setOldPrice(String oldPrice) { this.oldPrice = oldPrice; }

        public String getNewPrice() { return newPrice; }
        public void setNewPrice(String newPrice) { this.newPrice = newPrice; }

        public String getSummary() { return summary; }
        public void setSummary(String summary) { this.summary = summary; }
    }
}
